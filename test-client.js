// test-mcp-server.js
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import {
  ListToolsResultSchema,
  ListResourcesResultSchema,
  CallToolResultSchema,
  ReadResourceRequestSchema,
  ReadResourceResultSchema,
} from "@modelcontextprotocol/sdk/types.js";

async function testMCPServer() {
  // Create transport with command and args - let the SDK handle process spawning
  const transport = new StdioClientTransport({
    command: "node",
    args: ["dist/index.js"],
  });

  const client = new Client(
    {
      name: "test-client",
      version: "1.0.0",
    },
    {
      capabilities: {},
    }
  );

  try {
    await client.connect(transport);
    console.log("Connected to MCP server successfully!");

    // List available tools
    const tools = await client.request(
      { method: "tools/list" },
      ListToolsResultSchema
    );
    // console.log("Available tools:", JSON.stringify(tools, null, 2));

    // List available resources
    const resources = await client.request(
      { method: "resources/list" },
      ListResourcesResultSchema
    );
    // console.log("Available resources:", JSON.stringify(resources, null, 2));

    const resourceContent = await client.request(
      { method: "resources/read", params: { uri: "fincloud://components" } },
      ReadResourceResultSchema
    );
    console.log(
      "Available components:",
      JSON.stringify(resourceContent, null, 2)
    );

    // Test a simple tool call
    try {
      const searchResult = await client.request(
        {
          method: "tools/call",
          params: {
            name: "search_components",
            arguments: { query: "FinInput" },
          },
        },
        CallToolResultSchema
      );
      console.log(
        "Search result:",
        searchResult.content[0].text.substring(0, 100) + "..."
      );
    } catch (toolError) {
      console.log(
        "⚠️ Tool call failed (expected if no components indexed):",
        toolError.message
      );
    }

    console.log("✅ MCP server test completed successfully!");
  } catch (error) {
    console.error("Error:", error);
  } finally {
    // Clean up
    try {
      await transport.close();
      console.log("Transport closed successfully");
    } catch (closeError) {
      console.error("Error closing transport:", closeError);
    }
  }
}

testMCPServer();
